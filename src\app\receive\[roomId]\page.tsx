'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { TransferProgress } from '@/components/TransferProgress';
import { useWebRTC } from '@/hooks/useWebRTC';
import { importKeyFromBase64 } from '@/lib/crypto';
import type { EncryptionKey } from '@/types';

export default function ReceivePage() {
  const params = useParams();
  const roomId = params.roomId as string;
  
  const [encryptionKey, setEncryptionKey] = useState<EncryptionKey | null>(null);
  const [passphrase, setPassphrase] = useState('');
  const [showPassphraseInput, setShowPassphraseInput] = useState(false);
  const [keyError, setKeyError] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);

  const subscriptionToken = typeof window !== 'undefined' ? localStorage.getItem('navtransfer_token') : null;

  const {
    transferState,
    initializeConnection,
    createAnswer,
    handleIceCandidate,
    requestTransferStart,
    cancelTransfer,
    resetTransfer,
  } = useWebRTC({
    role: 'receiver',
    roomId,
    encryptionKey: encryptionKey || undefined,
    subscriptionToken: subscriptionToken || undefined,
  });

  // Extract encryption key from URL fragment
  useEffect(() => {
    const extractKey = async () => {
      const hash = window.location.hash.substring(1);
      const params = new URLSearchParams(hash);
      const keyParam = params.get('k');
      
      if (!keyParam) {
        setKeyError('No encryption key found in URL');
        return;
      }

      try {
        const key = await importKeyFromBase64(keyParam);
        setEncryptionKey(key);
        setIsReady(true);
      } catch (error) {
        setKeyError('Invalid encryption key');
        console.error('Failed to import encryption key:', error);
      }
    };

    extractKey();
  }, []);

  const handlePassphraseSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!passphrase.trim()) {
      setKeyError('Please enter the passphrase');
      return;
    }

    try {
      // TODO: Implement passphrase-based key unwrapping
      // For now, just proceed with the existing key
      setShowPassphraseInput(false);
      setIsReady(true);
    } catch (error) {
      setKeyError('Invalid passphrase');
    }
  }, [passphrase]);

  const handleStartReceiving = useCallback(async () => {
    if (!isReady) return;

    try {
      // Initialize WebRTC connection and start signaling
      await initializeConnection();

      // Request the sender to start the file transfer
      await requestTransferStart();
    } catch (error) {
      console.error('Failed to start receiving:', error);
    }
  }, [isReady, initializeConnection, requestTransferStart]);

  if (keyError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 text-red-500">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            Invalid Transfer Link
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {keyError}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Please check that you have the complete link from the sender.
          </p>
        </div>
      </div>
    );
  }

  if (showPassphraseInput) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <div className="text-center mb-6">
            <div className="w-16 h-16 mx-auto mb-4 text-blue-500">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Passphrase Required
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              This file is protected with a passphrase. Please enter it to continue.
            </p>
          </div>

          <form onSubmit={handlePassphraseSubmit} className="space-y-4">
            <div>
              <input
                type="password"
                placeholder="Enter passphrase"
                value={passphrase}
                onChange={(e) => setPassphrase(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                autoFocus
              />
            </div>
            
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Continue
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Navtransfer
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Ready to receive file
          </p>
        </header>

        <div className="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          {transferState.status === 'idle' && isReady && (
            <div className="text-center space-y-6">
              <div className="w-16 h-16 mx-auto text-blue-500">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 12l2 2 4-4" />
                </svg>
              </div>
              
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Ready to Receive
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Click the button below to connect with the sender and start receiving the file.
                </p>
              </div>

              <button
                onClick={handleStartReceiving}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Start Receiving
              </button>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <p className="text-blue-700 dark:text-blue-300 text-sm">
                  <strong>Privacy Notice:</strong> Your file will be transferred directly from the sender 
                  with end-to-end encryption. No data passes through our servers.
                </p>
              </div>
            </div>
          )}

          {transferState.status !== 'idle' && (
            <TransferProgress
              state={transferState}
              onCancel={cancelTransfer}
              onReset={resetTransfer}
            />
          )}
        </div>
      </div>
    </div>
  );
}
