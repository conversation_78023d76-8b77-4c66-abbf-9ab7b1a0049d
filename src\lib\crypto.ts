import type { Encryption<PERSON><PERSON>, Encrypted<PERSON>hunk } from '@/types';

/**
 * Generate a random 128-bit encryption key for AES-GCM
 */
export async function generateEncryptionKey(): Promise<EncryptionKey> {
  const key = await crypto.subtle.generateKey(
    {
      name: 'AES-GCM',
      length: 128,
    },
    true,
    ['encrypt', 'decrypt']
  );

  const iv = crypto.getRandomValues(new Uint8Array(12));

  return { key, iv };
}

/**
 * Export encryption key to base64url for URL fragment
 */
export async function exportKeyToBase64(encryptionKey: EncryptionKey): Promise<string> {
  const keyData = await crypto.subtle.exportKey('raw', encryptionKey.key);
  const combined = new Uint8Array(keyData.byteLength + encryptionKey.iv.length);
  combined.set(new Uint8Array(keyData), 0);
  combined.set(encryptionKey.iv, keyData.byteLength);

  // Convert to base64url (URL-safe base64)
  const base64 = btoa(String.fromCharCode(...combined));
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Import encryption key from base64url URL fragment
 */
export async function importKeyFromBase64(base64Key: string): Promise<EncryptionKey> {
  // Convert from base64url to standard base64
  let base64 = base64Key.replace(/-/g, '+').replace(/_/g, '/');

  // Add padding if needed
  while (base64.length % 4) {
    base64 += '=';
  }

  const combined = new Uint8Array(
    atob(base64)
      .split('')
      .map(char => char.charCodeAt(0))
  );

  const keyData = combined.slice(0, 16); // 128 bits = 16 bytes
  const iv = combined.slice(16);

  const key = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'AES-GCM' },
    false,
    ['encrypt', 'decrypt']
  );

  return { key, iv };
}

/**
 * Encrypt a file chunk using AES-GCM
 */
export async function encryptChunk(
  data: ArrayBuffer,
  encryptionKey: EncryptionKey,
  chunkIndex: number
): Promise<EncryptedChunk> {
  // Generate unique IV for this chunk by combining base IV with chunk index
  const chunkIv = new Uint8Array(12);
  chunkIv.set(encryptionKey.iv.slice(0, 8), 0);
  const indexBytes = new Uint8Array(4);
  new DataView(indexBytes.buffer).setUint32(0, chunkIndex, false);
  chunkIv.set(indexBytes, 8);

  const encryptedData = await crypto.subtle.encrypt(
    {
      name: 'AES-GCM',
      iv: chunkIv,
    },
    encryptionKey.key,
    data
  );

  return {
    data: encryptedData,
    iv: chunkIv,
    chunkIndex,
  };
}

/**
 * Decrypt a file chunk using AES-GCM
 */
export async function decryptChunk(
  encryptedChunk: EncryptedChunk,
  encryptionKey: EncryptionKey
): Promise<ArrayBuffer> {
  return await crypto.subtle.decrypt(
    {
      name: 'AES-GCM',
      iv: encryptedChunk.iv,
    },
    encryptionKey.key,
    encryptedChunk.data
  );
}

/**
 * Derive key from passphrase using PBKDF2
 */
export async function deriveKeyFromPassphrase(
  passphrase: string,
  salt: Uint8Array,
  iterations: number = 100000
): Promise<CryptoKey> {
  const encoder = new TextEncoder();
  const passphraseKey = await crypto.subtle.importKey(
    'raw',
    encoder.encode(passphrase),
    { name: 'PBKDF2' },
    false,
    ['deriveBits', 'deriveKey']
  );

  return await crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations,
      hash: 'SHA-256',
    },
    passphraseKey,
    { name: 'AES-GCM', length: 128 },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Wrap encryption key with passphrase
 */
export async function wrapKeyWithPassphrase(
  encryptionKey: EncryptionKey,
  passphrase: string
): Promise<{ wrappedKey: ArrayBuffer; salt: Uint8Array }> {
  const salt = crypto.getRandomValues(new Uint8Array(16));
  const wrappingKey = await deriveKeyFromPassphrase(passphrase, salt);
  
  const wrappedKey = await crypto.subtle.wrapKey(
    'raw',
    encryptionKey.key,
    wrappingKey,
    { name: 'AES-GCM', iv: encryptionKey.iv }
  );

  return { wrappedKey, salt };
}

/**
 * Unwrap encryption key with passphrase
 */
export async function unwrapKeyWithPassphrase(
  wrappedKey: ArrayBuffer,
  salt: Uint8Array,
  iv: Uint8Array,
  passphrase: string
): Promise<EncryptionKey> {
  const wrappingKey = await deriveKeyFromPassphrase(passphrase, salt);
  
  const key = await crypto.subtle.unwrapKey(
    'raw',
    wrappedKey,
    wrappingKey,
    { name: 'AES-GCM', iv },
    { name: 'AES-GCM' },
    false,
    ['encrypt', 'decrypt']
  );

  return { key, iv };
}
