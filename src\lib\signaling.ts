import type { SignalingMessage } from '@/types';

export type SignalingEventHandler = (message: SignalingMessage) => void;

export class SignalingClient {
  private roomId: string;
  private cursor: number = 0;
  private isPolling: boolean = false;
  private pollController: AbortController | null = null;
  private eventHandlers: Map<string, SignalingEventHandler[]> = new Map();

  constructor(roomId: string) {
    this.roomId = roomId;
  }

  /**
   * Add event handler for specific message types
   */
  on(messageType: string, handler: SignalingEventHandler) {
    if (!this.eventHandlers.has(messageType)) {
      this.eventHandlers.set(messageType, []);
    }
    this.eventHandlers.get(messageType)!.push(handler);
  }

  /**
   * Remove event handler
   */
  off(messageType: string, handler: SignalingEventHandler) {
    const handlers = this.eventHandlers.get(messageType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Send a signaling message
   */
  async send(message: Omit<SignalingMessage, 'timestamp'>): Promise<void> {
    const fullMessage: SignalingMessage = {
      ...message,
      timestamp: Date.now(),
    };

    console.log(`[Signaling] Sending message to room ${this.roomId}:`, fullMessage);

    try {
      const response = await fetch('/api/signal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          roomId: this.roomId,
          message: fullMessage,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        console.error(`[Signaling] Failed to send message, HTTP ${response.status}:`, error);
        throw new Error(error.error || 'Failed to send message');
      }

      console.log(`[Signaling] Message sent successfully to room ${this.roomId}`);
    } catch (error) {
      console.error('Failed to send signaling message:', error);
      throw error;
    }
  }

  /**
   * Start polling for messages
   */
  async startPolling(): Promise<void> {
    if (this.isPolling) {
      console.log(`[Signaling] Already polling for room ${this.roomId}`);
      return;
    }

    console.log(`[Signaling] Starting polling for room ${this.roomId}`);
    this.isPolling = true;
    this.pollController = new AbortController();

    try {
      while (this.isPolling) {
        await this.pollOnce();
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error(`[Signaling] Polling error for room ${this.roomId}:`, error);
        // Retry after a delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        if (this.isPolling) {
          console.log(`[Signaling] Retrying polling for room ${this.roomId}`);
          this.startPolling();
        }
      }
    }
  }

  /**
   * Stop polling for messages
   */
  stopPolling(): void {
    this.isPolling = false;
    if (this.pollController) {
      this.pollController.abort();
      this.pollController = null;
    }
  }

  /**
   * Poll once for new messages
   */
  private async pollOnce(): Promise<void> {
    if (!this.isPolling || !this.pollController) {
      return;
    }

    try {
      const url = new URL('/api/signal', window.location.origin);
      url.searchParams.set('room', this.roomId);
      url.searchParams.set('cursor', this.cursor.toString());

      const response = await fetch(url.toString(), {
        method: 'GET',
        signal: this.pollController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success && data.messages && data.messages.length > 0) {
        console.log(`[Signaling] Received ${data.messages.length} messages for room ${this.roomId}:`, data.messages);
        // Process new messages
        for (const message of data.messages) {
          this.handleMessage(message);
        }

        // Update cursor
        this.cursor = data.cursor;
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Polling was cancelled, this is expected
        return;
      }
      throw error;
    }
  }

  /**
   * Handle incoming message
   */
  private handleMessage(message: SignalingMessage): void {
    console.log(`[Signaling] Handling message type '${message.type}' for room ${this.roomId}:`, message);

    const handlers = this.eventHandlers.get(message.type);
    if (handlers) {
      console.log(`[Signaling] Found ${handlers.length} handlers for message type '${message.type}'`);
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error(`[Signaling] Error in message handler for type '${message.type}':`, error);
        }
      });
    } else {
      console.log(`[Signaling] No handlers found for message type '${message.type}'`);
    }

    // Also call handlers for 'all' message type
    const allHandlers = this.eventHandlers.get('*');
    if (allHandlers) {
      allHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in catch-all message handler:', error);
        }
      });
    }
  }

  /**
   * Clean up the room on the server
   */
  async cleanup(): Promise<void> {
    this.stopPolling();
    
    try {
      const url = new URL('/api/signal', window.location.origin);
      url.searchParams.set('room', this.roomId);

      await fetch(url.toString(), {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Failed to cleanup room:', error);
    }
  }

  /**
   * Get the room ID
   */
  getRoomId(): string {
    return this.roomId;
  }

  /**
   * Check if currently polling
   */
  isActive(): boolean {
    return this.isPolling;
  }
}
