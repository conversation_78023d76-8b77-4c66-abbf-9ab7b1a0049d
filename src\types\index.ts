// Subscription tiers
export type SubscriptionTier = 'free' | 'supporter' | 'pro';

// Subscription data structure
export interface Subscription {
  paypal_subscription_id: string;
  tier: SubscriptionTier;
  quotaGB: number;
  resetEpoch: number;
}

// File transfer types
export interface FileMetadata {
  id: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export interface TransferProgress {
  bytesTransferred: number;
  totalBytes: number;
  percentage: number;
  speed: number; // bytes per second
  estimatedTimeRemaining: number; // seconds
}

export interface TransferState {
  status: 'idle' | 'connecting' | 'connected' | 'transferring' | 'completed' | 'error' | 'cancelled';
  progress: TransferProgress;
  error?: string;
}

// WebRTC signaling types
export interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'file-metadata' | 'chunk-ack' | 'transfer-complete' | 'start-transfer';
  data: RTCSessionDescriptionInit | RTCIceCandidateInit | FileMetadata | { index: number } | null;
  timestamp: number;
}

export interface Room {
  id: string;
  createdAt: number;
  lastActivity: number;
  messages: SignalingMessage[];
}

// Encryption types
export interface EncryptionKey {
  key: CryptoKey;
  iv: Uint8Array;
}

export interface EncryptedChunk {
  data: ArrayBuffer;
  iv: Uint8Array;
  chunkIndex: number;
}

// TURN server configuration
export interface TurnConfig {
  urls: string[];
  username: string;
  credential: string;
}

// JWT payload for subscription tokens
export interface SubscriptionToken {
  tier: SubscriptionTier;
  quotaGB: number;
  exp: number;
  iat: number;
}

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface QuotaUsage {
  used: number;
  total: number;
  remaining: number;
}
