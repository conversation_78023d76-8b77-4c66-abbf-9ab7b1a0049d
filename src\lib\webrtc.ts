import type {
  FileMetadata,
  TransferProgress,
  EncryptionKey,
  EncryptedChunk,
  TurnConfig,
  SignalingMessage
} from '@/types';
import { encryptChunk, decryptChunk } from './crypto';
import { chunkFile, blobToArrayBuffer } from './utils';
import { SignalingClient } from './signaling';

export type WebRTCRole = 'sender' | 'receiver';

export interface WebRTCManagerOptions {
  role: WebRTCRole;
  roomId: string;
  encryptionKey?: EncryptionKey;
  onProgress?: (progress: TransferProgress) => void;
  onStatusChange?: (status: string) => void;
  onError?: (error: string) => void;
  onComplete?: () => void;
  turnConfig?: TurnConfig;
  subscriptionToken?: string;
}

export class WebRTCManager {
  private peerConnection: RTCPeerConnection;
  private dataChannel: RTCDataChannel | null = null;
  private dataChannelReady = false;
  private pendingFileTransfer: { file: File; metadata: FileMetadata } | null = null;
  private role: WebRTCRole;
  private roomId: string;
  private encryptionKey?: EncryptionKey;
  private onProgress?: (progress: TransferProgress) => void;
  private onStatusChange?: (status: string) => void;
  private onError?: (error: string) => void;
  private onComplete?: () => void;
  private signalingClient: SignalingClient;
  private subscriptionToken?: string;
  private turnCredentialsRequested = false;
  
  // Transfer state
  private fileMetadata?: FileMetadata;
  private fileChunks: Blob[] = [];
  private currentChunkIndex = 0;
  private totalChunks = 0;
  private bytesTransferred = 0;
  private startTime = 0;
  private receivedChunks: Map<number, ArrayBuffer> = new Map();

  constructor(options: WebRTCManagerOptions) {
    this.role = options.role;
    this.roomId = options.roomId;
    this.encryptionKey = options.encryptionKey;
    this.onProgress = options.onProgress;
    this.onStatusChange = options.onStatusChange;
    this.onError = options.onError;
    this.onComplete = options.onComplete;
    this.subscriptionToken = options.subscriptionToken;

    // Initialize signaling client only if we have a valid roomId
    if (this.roomId && this.roomId !== 'placeholder' && this.roomId !== 'temp') {
      this.signalingClient = new SignalingClient(this.roomId);
      this.setupSignalingHandlers();
    } else {
      // Create a dummy signaling client that will be replaced later
      this.signalingClient = new SignalingClient('placeholder');
    }

    // Configure ICE servers
    const iceServers: RTCIceServer[] = [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ];

    // Add TURN server if available
    if (options.turnConfig) {
      iceServers.push({
        urls: options.turnConfig.urls,
        username: options.turnConfig.username,
        credential: options.turnConfig.credential,
      });
    }

    this.peerConnection = new RTCPeerConnection({
      iceServers,
      iceCandidatePoolSize: 10,
    });

    this.setupPeerConnection();
  }

  private setupSignalingHandlers() {
    this.signalingClient.on('offer', async (message) => {
      if (this.role === 'receiver') {
        try {
          const answer = await this.createAnswer(message.data);
          await this.signalingClient.send({
            type: 'answer',
            data: answer,
          });
        } catch (error) {
          this.onError?.(`Failed to handle offer: ${error}`);
        }
      }
    });

    this.signalingClient.on('answer', async (message) => {
      if (this.role === 'sender') {
        try {
          await this.handleAnswer(message.data);
        } catch (error) {
          this.onError?.(`Failed to handle answer: ${error}`);
        }
      }
    });

    this.signalingClient.on('ice-candidate', async (message) => {
      try {
        await this.handleIceCandidate(message.data);
      } catch (error) {
        this.onError?.(`Failed to handle ICE candidate: ${error}`);
      }
    });

    this.signalingClient.on('start-transfer', async (message) => {
      if (this.role === 'sender' && this.pendingFileTransfer) {
        try {
          // Receiver has requested to start the transfer
          await this.startFileTransfer(this.pendingFileTransfer.file, this.pendingFileTransfer.metadata);
        } catch (error) {
          this.onError?.(`Failed to start transfer: ${error}`);
        }
      }
    });
  }

  private setupPeerConnection() {
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.signalingClient.send({
          type: 'ice-candidate',
          data: event.candidate,
        }).catch(error => {
          this.onError?.(`Failed to send ICE candidate: ${error}`);
        });
      }
    };

    this.peerConnection.onconnectionstatechange = async () => {
      const state = this.peerConnection.connectionState;
      this.onStatusChange?.(state);

      if (state === 'connected') {
        this.onStatusChange?.('Connected - starting transfer');
      } else if (state === 'failed') {
        // Try to get TURN credentials and retry if available
        if (!this.turnCredentialsRequested && this.subscriptionToken) {
          this.onStatusChange?.('P2P failed, trying TURN relay...');
          await this.retryWithTurnServer();
        } else {
          this.onError?.('Connection failed - consider upgrading for TURN relay support');
        }
      } else if (state === 'disconnected') {
        this.onError?.('Connection disconnected');
      }
    };

    if (this.role === 'sender') {
      this.setupSender();
    } else {
      this.setupReceiver();
    }
  }

  private setupSender() {
    // Create data channel for file transfer
    this.dataChannel = this.peerConnection.createDataChannel('fileTransfer', {
      ordered: true,
      maxRetransmits: 3,
    });

    this.dataChannel.onopen = () => {
      this.dataChannelReady = true;
      this.onStatusChange?.('Data channel opened');

      // If there's a pending file transfer, start it now
      if (this.pendingFileTransfer) {
        this.startFileTransferInternal(this.pendingFileTransfer.file, this.pendingFileTransfer.metadata);
        this.pendingFileTransfer = null;
      }
    };

    this.dataChannel.onclose = () => {
      this.dataChannelReady = false;
      this.onStatusChange?.('Data channel closed');
    };

    this.dataChannel.onerror = (error) => {
      this.dataChannelReady = false;
      this.onError?.(`Data channel error: ${error}`);
    };
  }

  private setupReceiver() {
    this.peerConnection.ondatachannel = (event) => {
      this.dataChannel = event.channel;

      this.dataChannel.onmessage = async (event) => {
        await this.handleReceivedMessage(event.data);
      };

      this.dataChannel.onopen = () => {
        this.dataChannelReady = true;
        this.onStatusChange?.('Ready to receive file');
      };

      this.dataChannel.onclose = () => {
        this.dataChannelReady = false;
        this.onStatusChange?.('Data channel closed');
      };

      this.dataChannel.onerror = (error) => {
        this.dataChannelReady = false;
        this.onError?.(`Data channel error: ${error}`);
      };
    };
  }

  async createOffer(): Promise<RTCSessionDescriptionInit> {
    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);

    // Send offer through signaling
    await this.signalingClient.send({
      type: 'offer',
      data: offer,
    });

    return offer;
  }

  async createAnswer(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    await this.peerConnection.setRemoteDescription(offer);
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);
    return answer;
  }

  async handleAnswer(answer: RTCSessionDescriptionInit) {
    await this.peerConnection.setRemoteDescription(answer);
  }

  async handleIceCandidate(candidate: RTCIceCandidateInit) {
    await this.peerConnection.addIceCandidate(candidate);
  }

  async requestTransferStart() {
    if (this.role !== 'receiver') {
      throw new Error('Only receiver can request transfer start');
    }

    // Send signal to sender to start the transfer
    await this.signalingClient.send({
      type: 'start-transfer',
      data: null,
    });
  }

  async startFileTransfer(file: File, metadata: FileMetadata) {
    if (this.role !== 'sender') {
      throw new Error('Cannot start transfer: not a sender');
    }

    if (!this.encryptionKey) {
      throw new Error('Encryption key required for file transfer');
    }

    // Check if data channel is ready
    if (!this.dataChannelReady || !this.dataChannel || this.dataChannel.readyState !== 'open') {
      // Store the transfer for when the data channel opens
      this.pendingFileTransfer = { file, metadata };
      this.onStatusChange?.('Waiting for connection to be ready...');
      return;
    }

    // Data channel is ready, start transfer immediately
    await this.startFileTransferInternal(file, metadata);
  }

  private async startFileTransferInternal(file: File, metadata: FileMetadata) {
    if (!this.dataChannel || !this.encryptionKey) {
      throw new Error('Data channel or encryption key not available');
    }

    this.fileMetadata = metadata;
    this.fileChunks = Array.from(chunkFile(file, 256 * 1024)); // 256KB chunks
    this.totalChunks = this.fileChunks.length;
    this.currentChunkIndex = 0;
    this.bytesTransferred = 0;
    this.startTime = Date.now();

    // Send file metadata first
    this.sendMessage({
      type: 'file-metadata',
      data: metadata,
      timestamp: Date.now(),
    });

    // Start sending chunks
    await this.sendNextChunk();
  }

  private async sendNextChunk() {
    if (this.currentChunkIndex >= this.totalChunks) {
      this.sendMessage({
        type: 'transfer-complete',
        data: null,
        timestamp: Date.now(),
      });
      this.onComplete?.();
      return;
    }

    if (!this.dataChannel || !this.encryptionKey) return;

    const chunk = this.fileChunks[this.currentChunkIndex];
    const chunkData = await blobToArrayBuffer(chunk);
    
    // Encrypt the chunk
    const encryptedChunk = await encryptChunk(
      chunkData,
      this.encryptionKey,
      this.currentChunkIndex
    );

    // Send encrypted chunk
    this.dataChannel.send(JSON.stringify({
      type: 'chunk',
      index: this.currentChunkIndex,
      data: Array.from(new Uint8Array(encryptedChunk.data)),
      iv: Array.from(encryptedChunk.iv),
    }));

    this.currentChunkIndex++;
    this.bytesTransferred += chunk.size;

    // Update progress
    this.updateProgress();
  }

  private async handleReceivedMessage(data: string | ArrayBuffer) {
    try {
      const message = JSON.parse(data as string);
      
      switch (message.type) {
        case 'file-metadata':
          this.fileMetadata = message.data;
          this.onStatusChange?.(`Receiving: ${message.data.name}`);
          break;
          
        case 'chunk':
          await this.handleReceivedChunk(message);
          break;
          
        case 'transfer-complete':
          await this.assembleFile();
          this.onComplete?.();
          break;
      }
    } catch (error) {
      this.onError?.(`Failed to handle message: ${error}`);
    }
  }

  private async handleReceivedChunk(message: any) {
    if (!this.encryptionKey) {
      this.onError?.('No encryption key available for decryption');
      return;
    }

    try {
      // Reconstruct encrypted chunk
      const encryptedChunk = {
        data: new Uint8Array(message.data).buffer,
        iv: new Uint8Array(message.iv),
        chunkIndex: message.index,
      };

      // Decrypt chunk
      const decryptedData = await decryptChunk(encryptedChunk, this.encryptionKey);
      this.receivedChunks.set(message.index, decryptedData);

      this.bytesTransferred += decryptedData.byteLength;
      this.updateProgress();

      // Send acknowledgment
      this.sendMessage({
        type: 'chunk-ack',
        data: { index: message.index },
        timestamp: Date.now(),
      });

    } catch (error) {
      this.onError?.(`Failed to decrypt chunk: ${error}`);
    }
  }

  private async assembleFile() {
    if (!this.fileMetadata) return;

    const chunks: ArrayBuffer[] = [];
    for (let i = 0; i < this.receivedChunks.size; i++) {
      const chunk = this.receivedChunks.get(i);
      if (chunk) {
        chunks.push(chunk);
      }
    }

    const blob = new Blob(chunks, { type: this.fileMetadata.type });
    const url = URL.createObjectURL(blob);
    
    // Trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = this.fileMetadata.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  private updateProgress() {
    if (!this.fileMetadata) return;

    const now = Date.now();
    const elapsed = (now - this.startTime) / 1000; // seconds
    const speed = elapsed > 0 ? this.bytesTransferred / elapsed : 0;
    const remaining = this.fileMetadata.size - this.bytesTransferred;
    const eta = speed > 0 ? remaining / speed : 0;

    const progress: TransferProgress = {
      bytesTransferred: this.bytesTransferred,
      totalBytes: this.fileMetadata.size,
      percentage: (this.bytesTransferred / this.fileMetadata.size) * 100,
      speed,
      estimatedTimeRemaining: eta,
    };

    this.onProgress?.(progress);
  }

  private sendMessage(message: any) {
    if (this.dataChannel && this.dataChannel.readyState === 'open' && this.dataChannelReady) {
      this.dataChannel.send(JSON.stringify(message));
    } else {
      console.warn('Cannot send message: data channel not ready', {
        hasDataChannel: !!this.dataChannel,
        readyState: this.dataChannel?.readyState,
        dataChannelReady: this.dataChannelReady
      });
    }
  }

  updateRoomId(newRoomId: string): void {
    if (this.roomId !== newRoomId) {
      this.roomId = newRoomId;

      // Stop existing signaling client
      if (this.signalingClient) {
        this.signalingClient.stopPolling();
      }

      // Create new signaling client with the new roomId
      this.signalingClient = new SignalingClient(this.roomId);
      this.setupSignalingHandlers();
    }
  }

  async startSignaling(): Promise<void> {
    // Validate roomId before starting signaling
    if (!this.roomId || this.roomId === 'placeholder' || this.roomId === 'temp') {
      throw new Error('Cannot start signaling without a valid room ID');
    }

    await this.signalingClient.startPolling();
  }

  private async retryWithTurnServer(): Promise<void> {
    if (!this.subscriptionToken) {
      return;
    }

    this.turnCredentialsRequested = true;

    try {
      // Get TURN credentials from API
      const response = await fetch('/api/turn', {
        headers: {
          'Authorization': `Bearer ${this.subscriptionToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to get TURN credentials');
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to get TURN credentials');
      }

      const turnConfig: TurnConfig = data.data;

      // Create new peer connection with TURN servers
      const iceServers: RTCIceServer[] = [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        {
          urls: turnConfig.urls,
          username: turnConfig.username,
          credential: turnConfig.credential,
        },
      ];

      // Close existing connection
      this.peerConnection.close();

      // Create new connection with TURN
      this.peerConnection = new RTCPeerConnection({
        iceServers,
        iceCandidatePoolSize: 10,
      });

      // Re-setup connection handlers
      this.setupPeerConnection();

      // Restart the connection process
      if (this.role === 'sender') {
        await this.createOffer();
      }

      this.onStatusChange?.('Retrying with TURN relay...');

    } catch (error) {
      console.error('Failed to retry with TURN server:', error);
      this.onError?.('Failed to establish connection even with TURN relay');
    }
  }

  close() {
    this.signalingClient.cleanup();
    if (this.dataChannel) {
      this.dataChannel.close();
    }
    this.peerConnection.close();
  }
}
