'use client';

import { cn, formatFileSize, formatSpeed, formatDuration } from '@/lib/utils';
import type { TransferState } from '@/types';

interface TransferProgressProps {
  state: TransferState;
  onCancel: () => void;
  onReset: () => void;
}

export function TransferProgress({ state, onCancel, onReset }: TransferProgressProps) {
  const { status, progress, error } = state;

  const getStatusColor = () => {
    switch (status) {
      case 'connecting':
        return 'text-blue-600 dark:text-blue-400';
      case 'transferring':
        return 'text-green-600 dark:text-green-400';
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'cancelled':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connecting':
        return 'Connecting to recipient...';
      case 'connected':
        return 'Connected! Waiting for transfer to start...';
      case 'transferring':
        return 'Transferring file...';
      case 'completed':
        return 'Transfer completed successfully!';
      case 'error':
        return 'Transfer failed';
      case 'cancelled':
        return 'Transfer cancelled';
      default:
        return 'Preparing transfer...';
    }
  };

  const showProgress = status === 'transferring' || status === 'completed';
  const showActions = status === 'connecting' || status === 'connected' || status === 'transferring';
  const showReset = status === 'completed' || status === 'error' || status === 'cancelled';

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className={cn("text-lg font-medium", getStatusColor())}>
          {getStatusText()}
        </div>
        
        {status === 'connecting' && (
          <div className="mt-4 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {showProgress && (
        <div className="space-y-4">
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className={cn(
                "h-3 rounded-full transition-all duration-300",
                status === 'completed' 
                  ? "bg-green-500" 
                  : "bg-blue-500"
              )}
              style={{ width: `${progress.percentage}%` }}
            />
          </div>

          {/* Progress Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-medium text-gray-900 dark:text-white">
                {progress.percentage.toFixed(1)}%
              </div>
              <div className="text-gray-500 dark:text-gray-400">Complete</div>
            </div>
            
            <div className="text-center">
              <div className="font-medium text-gray-900 dark:text-white">
                {formatFileSize(progress.bytesTransferred)}
              </div>
              <div className="text-gray-500 dark:text-gray-400">
                of {formatFileSize(progress.totalBytes)}
              </div>
            </div>
            
            {status === 'transferring' && (
              <>
                <div className="text-center">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {formatSpeed(progress.speed)}
                  </div>
                  <div className="text-gray-500 dark:text-gray-400">Speed</div>
                </div>
                
                <div className="text-center">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {formatDuration(progress.estimatedTimeRemaining)}
                  </div>
                  <div className="text-gray-500 dark:text-gray-400">Remaining</div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {status === 'error' && error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
        </div>
      )}

      {/* Success Message */}
      {status === 'completed' && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-green-700 dark:text-green-300 text-sm font-medium">
                File transferred successfully! All recipients have received the file.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        {showActions && (
          <button
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel Transfer
          </button>
        )}
        
        {showReset && (
          <button
            onClick={onReset}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Transfer Another File
          </button>
        )}
      </div>

      {/* Keep Tab Open Warning */}
      {(status === 'connecting' || status === 'transferring') && (
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-amber-700 dark:text-amber-300 text-sm">
                <strong>Keep this tab open!</strong> Closing this tab will interrupt the transfer.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
