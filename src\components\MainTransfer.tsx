'use client';

import { useState, useCallback, useEffect } from 'react';
import { FileDropzone } from './FileDropzone';
import { TransferProgress } from './TransferProgress';
import { ShareLink } from './ShareLink';
import { useWebRTC } from '@/hooks/useWebRTC';
import type { FileMetadata, TransferState, EncryptionKey } from '@/types';
import { generateRoomId, generateFileId, getFileExtension, validateFileSize } from '@/lib/utils';
import { generateEncryptionKey, exportKeyToBase64 } from '@/lib/crypto';

export function MainTransfer() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [shareLink, setShareLink] = useState<string>('');
  const [roomId, setRoomId] = useState<string>('');
  const [encryptionKey, setEncryptionKey] = useState<EncryptionKey | null>(null);
  const [fileMetadata, setFileMetadata] = useState<FileMetadata | null>(null);
  const [transferState, setTransferState] = useState<TransferState>({
    status: 'idle',
    progress: 0,
    speed: 0,
    error: null,
  });

  const {
    transferState: webrtcTransferState,
    initializeConnection,
    createOffer,
    handleAnswer,
    handleIceCandidate,
    startFileTransfer,
    cancelTransfer: webrtcCancel,
    resetTransfer: webrtcReset,
  } = useWebRTC({
    role: 'sender',
    roomId: roomId || 'placeholder', // Use placeholder until real roomId is set
    encryptionKey: encryptionKey || undefined,
  });

  // Initialize WebRTC connection when roomId and encryptionKey are available
  useEffect(() => {
    console.log('[MainTransfer] useEffect check:', {
      roomId: !!roomId,
      encryptionKey: !!encryptionKey,
      selectedFile: !!selectedFile,
      fileMetadata: !!fileMetadata,
      status: transferState.status
    });

    if (roomId && encryptionKey && selectedFile && fileMetadata && transferState.status === 'waiting') {
      console.log('[MainTransfer] All conditions met, initializing WebRTC...');
      const initializeWebRTC = async () => {
        try {
          // First initialize the connection with the new roomId and encryptionKey
          console.log('[MainTransfer] Step 1: Initializing connection...');
          await initializeConnection();

          // Then create the offer
          console.log('[MainTransfer] Step 2: Creating offer...');
          await createOffer();

          // Prepare the file for transfer (this will be stored until receiver requests it)
          console.log('[MainTransfer] Step 3: Starting file transfer...');
          await startFileTransfer(selectedFile, fileMetadata);

          console.log('[MainTransfer] WebRTC initialization complete');
        } catch (error) {
          console.error('Error initializing WebRTC:', error);
          setTransferState(prev => ({
            ...prev,
            status: 'error',
            error: 'Failed to initialize connection',
          }));
        }
      };

      initializeWebRTC();
    } else {
      console.log('[MainTransfer] Conditions not met for WebRTC initialization');
    }
  }, [roomId, encryptionKey, selectedFile, fileMetadata, transferState.status, initializeConnection, createOffer, startFileTransfer]);

  // Sync WebRTC transfer state with local transfer state
  useEffect(() => {
    if (webrtcTransferState.status === 'transferring') {
      setTransferState(prev => ({
        ...prev,
        status: 'transferring',
        progress: webrtcTransferState.progress.percentage,
        speed: webrtcTransferState.progress.speed,
      }));
    } else if (webrtcTransferState.status === 'completed') {
      setTransferState(prev => ({
        ...prev,
        status: 'completed',
        progress: 100,
      }));
    } else if (webrtcTransferState.status === 'error') {
      setTransferState(prev => ({
        ...prev,
        status: 'error',
        error: 'Transfer failed',
      }));
    }
  }, [webrtcTransferState]);

  const handleFileSelect = useCallback((file: File) => {
    const validation = validateFileSize(file);
    if (!validation.valid) {
      setTransferState({
        status: 'error',
        progress: 0,
        speed: 0,
        error: validation.error || 'File validation failed',
      });
      return;
    }

    setSelectedFile(file);
    setTransferState({
      status: 'idle',
      progress: 0,
      speed: 0,
      error: null,
    });
  }, []);

  const handleCreateShareLink = useCallback(async () => {
    if (!selectedFile) return;

    try {
      // Generate encryption key and room ID
      const newEncryptionKey = await generateEncryptionKey();
      const keyBase64 = await exportKeyToBase64(newEncryptionKey);
      const newRoomId = generateRoomId();
      const fileId = generateFileId();

      // Update state with new values
      setRoomId(newRoomId);
      setEncryptionKey(newEncryptionKey);

      // Create file metadata
      const metadata: FileMetadata = {
        id: fileId,
        name: selectedFile.name,
        size: selectedFile.size,
        type: selectedFile.type,
        extension: getFileExtension(selectedFile.name),
        checksum: '', // Will be calculated during transfer
      };

      // Store metadata for later use
      setFileMetadata(metadata);

      // Create share link with encryption key in fragment
      const baseUrl = `${window.location.origin}/receive/${newRoomId}`;
      const params = new URLSearchParams({
        name: metadata.name,
        size: metadata.size.toString(),
        type: metadata.type,
      });
      const link = `${baseUrl}?${params}#k=${keyBase64}`;

      setShareLink(link);
      setTransferState(prev => ({
        ...prev,
        status: 'waiting',
      }));

      // Initialize WebRTC connection - we'll need to wait for the useWebRTC hook to reinitialize
      // with the new roomId and encryptionKey, then call createOffer
      // This will be handled in a useEffect

    } catch (error) {
      console.error('Error creating share link:', error);
      setTransferState(prev => ({
        ...prev,
        status: 'error',
        error: 'Failed to create share link',
      }));
    }
  }, [selectedFile]);



  const handleCancelTransfer = useCallback(() => {
    webrtcCancel();
    setTransferState({
      status: 'idle',
      progress: 0,
      speed: 0,
      error: null,
    });
  }, [webrtcCancel]);

  const handleReset = useCallback(() => {
    webrtcReset();
    setSelectedFile(null);
    setShareLink('');
    setRoomId('');
    setEncryptionKey(null);
    setTransferState({
      status: 'idle',
      progress: 0,
      speed: 0,
      error: null,
    });
  }, [webrtcReset]);

  return (
    <div className="h-full flex flex-col">
      {/* Hero Section */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-2xl">
          {!selectedFile ? (
            <div className="text-center space-y-6">
              <div className="space-y-3">
                <h1 className="text-4xl font-bold text-white">
                  Send files instantly
                </h1>
                <p className="text-lg text-slate-400 max-w-lg mx-auto">
                  Secure, private, peer-to-peer file sharing.
                  No uploads, no downloads, just direct transfer.
                </p>
              </div>

              <FileDropzone onFileSelect={handleFileSelect} />

              {/* Quick Features */}
              <div className="flex justify-center space-x-6 text-sm text-slate-500">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                  <span>End-to-end encrypted</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <span>No file size limits</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full" />
                  <span>Works globally</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* File Info */}
              <div className="bg-slate-800 rounded-2xl p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-white">{selectedFile.name}</h3>
                    <p className="text-sm text-slate-400">
                      {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB • {getFileExtension(selectedFile.name).toUpperCase()}
                    </p>
                  </div>
                  <button
                    onClick={handleReset}
                    className="p-2 text-slate-400 hover:text-slate-300 hover:bg-slate-700 rounded-lg transition-colors"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {!shareLink && transferState.status === 'idle' && (
                <button
                  onClick={handleCreateShareLink}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
                >
                  Create Share Link
                </button>
              )}

              {shareLink && transferState.status === 'waiting' && (
                <ShareLink link={shareLink} />
              )}

              {(transferState.status === 'connecting' ||
                transferState.status === 'transferring' ||
                transferState.status === 'completed' ||
                transferState.status === 'error') && (
                <TransferProgress
                  state={transferState}
                  onCancel={handleCancelTransfer}
                  onReset={handleReset}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
