import { useState, useCallback, useRef, useEffect } from 'react';
import { WebRTCManager, type WebRTCRole } from '@/lib/webrtc';
import type { 
  FileMetadata, 
  TransferProgress, 
  TransferState, 
  EncryptionKey,
  TurnConfig 
} from '@/types';

interface UseWebRTCOptions {
  role: WebRTCRole;
  roomId: string;
  encryptionKey?: EncryptionKey;
  turnConfig?: TurnConfig;
  subscriptionToken?: string;
}

export function useWebRTC({ role, roomId, encryptionKey, turnConfig, subscriptionToken }: UseWebRTCOptions) {
  const [transferState, setTransferState] = useState<TransferState>({
    status: 'idle',
    progress: {
      bytesTransferred: 0,
      totalBytes: 0,
      percentage: 0,
      speed: 0,
      estimatedTimeRemaining: 0,
    },
  });

  const webrtcManager = useRef<WebRTCManager | null>(null);

  const handleProgress = useCallback((progress: TransferProgress) => {
    setTransferState(prev => ({
      ...prev,
      progress,
      status: 'transferring',
    }));
  }, []);

  const handleStatusChange = useCallback((status: string) => {
    console.log('WebRTC status:', status);

    if (status === 'Connected') {
      setTransferState(prev => ({
        ...prev,
        status: 'connected',
      }));
    } else if (status.includes('starting transfer') || status.includes('Starting transfer')) {
      setTransferState(prev => ({
        ...prev,
        status: 'transferring',
      }));
    } else if (status.includes('connecting') || status.includes('Connecting')) {
      setTransferState(prev => ({
        ...prev,
        status: 'connecting',
      }));
    }
  }, []);

  const handleError = useCallback((error: string) => {
    setTransferState(prev => ({
      ...prev,
      status: 'error',
      error,
    }));
  }, []);

  const handleComplete = useCallback(() => {
    setTransferState(prev => ({
      ...prev,
      status: 'completed',
    }));
  }, []);

  const initializeConnection = useCallback(async () => {
    console.log(`[useWebRTC] Initializing connection for ${role} with roomId: ${roomId}`);

    if (!webrtcManager.current) {
      console.log(`[useWebRTC] Creating new WebRTCManager for ${role}`);
      webrtcManager.current = new WebRTCManager({
        role,
        roomId,
        encryptionKey,
        turnConfig,
        subscriptionToken,
        onProgress: handleProgress,
        onStatusChange: handleStatusChange,
        onError: handleError,
        onComplete: handleComplete,
      });
    } else {
      console.log(`[useWebRTC] Updating existing manager with new roomId: ${roomId}`);
      // Update the existing manager with new roomId if it changed
      webrtcManager.current.updateRoomId(roomId);
    }

    setTransferState(prev => ({
      ...prev,
      status: 'connecting',
    }));

    console.log(`[useWebRTC] Starting signaling for ${role}`);
    // Start signaling
    try {
      await webrtcManager.current.startSignaling();
      console.log(`[useWebRTC] Signaling started successfully for ${role}`);
    } catch (error) {
      console.error(`[useWebRTC] Failed to start signaling for ${role}:`, error);
      throw error;
    }

    console.log(`[useWebRTC] Connection initialized successfully for ${role}`);
    return webrtcManager.current;
  }, [role, roomId, encryptionKey, turnConfig, subscriptionToken, handleProgress, handleStatusChange, handleError, handleComplete]);

  const createOffer = useCallback(async () => {
    console.log(`[useWebRTC] createOffer called for ${role}`);
    const manager = webrtcManager.current || await initializeConnection();
    console.log(`[useWebRTC] Calling manager.createOffer() for ${role}`);
    const result = await manager.createOffer();
    console.log(`[useWebRTC] createOffer completed for ${role}`, result);
    return result;
  }, [role, initializeConnection]);

  const createAnswer = useCallback(async (offer: RTCSessionDescriptionInit) => {
    const manager = webrtcManager.current || await initializeConnection();
    return await manager.createAnswer(offer);
  }, [initializeConnection]);

  const handleAnswer = useCallback(async (answer: RTCSessionDescriptionInit) => {
    if (!webrtcManager.current) {
      throw new Error('WebRTC manager not initialized');
    }
    await webrtcManager.current.handleAnswer(answer);
  }, []);

  const handleIceCandidate = useCallback(async (candidate: RTCIceCandidateInit) => {
    if (!webrtcManager.current) {
      throw new Error('WebRTC manager not initialized');
    }
    await webrtcManager.current.handleIceCandidate(candidate);
  }, []);

  const startFileTransfer = useCallback(async (file: File, metadata: FileMetadata) => {
    if (!webrtcManager.current) {
      throw new Error('WebRTC manager not initialized');
    }

    if (role !== 'sender') {
      throw new Error('Only sender can start file transfer');
    }

    await webrtcManager.current.startFileTransfer(file, metadata);
  }, [role]);

  const requestTransferStart = useCallback(async () => {
    console.log(`[useWebRTC] Requesting transfer start for ${role}`);
    if (!webrtcManager.current) {
      throw new Error('WebRTC manager not initialized');
    }

    if (role !== 'receiver') {
      throw new Error('Only receiver can request transfer start');
    }

    await webrtcManager.current.requestTransferStart();
    console.log(`[useWebRTC] Transfer start request sent for ${role}`);
  }, [role]);

  const cancelTransfer = useCallback(() => {
    if (webrtcManager.current) {
      webrtcManager.current.close();
      webrtcManager.current = null;
    }
    
    setTransferState(prev => ({
      ...prev,
      status: 'cancelled',
    }));
  }, []);

  const resetTransfer = useCallback(() => {
    if (webrtcManager.current) {
      webrtcManager.current.close();
      webrtcManager.current = null;
    }
    
    setTransferState({
      status: 'idle',
      progress: {
        bytesTransferred: 0,
        totalBytes: 0,
        percentage: 0,
        speed: 0,
        estimatedTimeRemaining: 0,
      },
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (webrtcManager.current) {
        webrtcManager.current.close();
      }
    };
  }, []);

  return {
    transferState,
    initializeConnection,
    createOffer,
    createAnswer,
    handleAnswer,
    handleIceCandidate,
    startFileTransfer,
    requestTransferStart,
    cancelTransfer,
    resetTransfer,
  };
}
