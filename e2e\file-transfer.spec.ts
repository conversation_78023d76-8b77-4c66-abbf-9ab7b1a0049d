import { test, expect } from '@playwright/test';

test.describe('File Transfer', () => {
  test('should display the main page correctly', async ({ page }) => {
    await page.goto('/');

    // Check main heading
    await expect(page.getByRole('heading', { name: 'Navtransfer' })).toBeVisible();

    // Check privacy description
    await expect(page.getByText('Privacy-first file sharing')).toBeVisible();

    // Check file dropzone
    await expect(page.getByText('Drop your file here')).toBeVisible();

    // Check security features
    await expect(page.getByText('End-to-end encrypted')).toBeVisible();
    await expect(page.getByText('Direct transfer')).toBeVisible();
    await expect(page.getByText('Privacy first')).toBeVisible();
  });

  test('should show subscription banner for free tier', async ({ page }) => {
    await page.goto('/');

    // Check subscription banner is visible
    await expect(page.getByText('Upgrade for Reliable Transfers')).toBeVisible();
    await expect(page.getByText('Supporter - $1/month')).toBeVisible();
    await expect(page.getByText('Pro - $2/month')).toBeVisible();
  });

  test('should handle file selection', async ({ page }) => {
    await page.goto('/');

    // Create a test file
    const fileContent = 'This is a test file content for Navtransfer';
    const fileName = 'test-file.txt';

    // Upload file via file input
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: fileName,
      mimeType: 'text/plain',
      buffer: Buffer.from(fileContent),
    });

    // Check that file is selected and UI updates
    await expect(page.getByText('Selected File')).toBeVisible();
    await expect(page.getByText(fileName)).toBeVisible();
    await expect(page.getByText('Share this link')).toBeVisible();
    await expect(page.getByText('Start Transfer')).toBeVisible();
  });

  test('should generate share link with encryption key', async ({ page }) => {
    await page.goto('/');

    const fileContent = 'Test file for share link generation';
    const fileName = 'share-test.txt';

    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: fileName,
      mimeType: 'text/plain',
      buffer: Buffer.from(fileContent),
    });

    // Wait for share link to be generated
    await expect(page.getByText('Share this link')).toBeVisible();

    // Check that link contains room ID and encryption key
    const linkInput = page.locator('input[readonly]');
    const linkValue = await linkInput.inputValue();
    
    expect(linkValue).toMatch(/\/receive\/[a-f0-9]{32}#k=/);
    expect(linkValue).toContain('#k='); // Encryption key in fragment
  });

  test('should show privacy warnings and instructions', async ({ page }) => {
    await page.goto('/');

    const fileContent = 'Test file for privacy check';
    const fileName = 'privacy-test.txt';

    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: fileName,
      mimeType: 'text/plain',
      buffer: Buffer.from(fileContent),
    });

    // Check privacy warnings
    await expect(page.getByText('Keep this tab open')).toBeVisible();
    await expect(page.getByText('Closing the tab will cancel the transfer')).toBeVisible();
  });

  test('should handle passphrase protection', async ({ page }) => {
    await page.goto('/');

    const fileContent = 'Test file for passphrase protection';
    const fileName = 'protected-test.txt';

    // Upload file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: fileName,
      mimeType: 'text/plain',
      buffer: Buffer.from(fileContent),
    });

    // Enable passphrase protection
    await page.getByLabel('Protect with passphrase').check();
    
    // Enter passphrase
    const passphraseInput = page.getByPlaceholder('Enter passphrase');
    await expect(passphraseInput).toBeVisible();
    await passphraseInput.fill('test-passphrase-123');

    // Check that passphrase is set
    await expect(passphraseInput).toHaveValue('test-passphrase-123');
  });

  test('should navigate to receiver page', async ({ page }) => {
    // Test receiver page with a mock room ID and encryption key
    const roomId = 'a1b2c3d4e5f6789012345678901234ab';
    // Valid base64url key: 16 bytes key + 12 bytes IV = 28 bytes total
    // This represents a valid AES-GCM key structure in base64url format
    const encryptionKey = 'AQIDBAUGBwgJCgsMDQ4PEBESExQVFhcYGRobHA'; // base64url encoded test key (28 bytes)

    await page.goto(`/receive/${roomId}#k=${encryptionKey}`);

    // Check receiver page elements
    await expect(page.getByRole('heading', { name: 'Navtransfer' })).toBeVisible();
    await expect(page.getByText('Ready to receive file')).toBeVisible();
    await expect(page.getByText('Start Receiving')).toBeVisible();

    // Check privacy notice
    await expect(page.getByText('Privacy Notice')).toBeVisible();
    await expect(page.getByText('end-to-end encryption')).toBeVisible();
  });

  test('should handle invalid receiver links', async ({ page }) => {
    // Test with invalid room ID
    await page.goto('/receive/invalid-room-id');

    await expect(page.getByText('Invalid Transfer Link')).toBeVisible();
    await expect(page.getByText('No encryption key found in URL')).toBeVisible();
  });

  test('should display privacy policy', async ({ page }) => {
    await page.goto('/privacy');

    // Check privacy policy content
    await expect(page.getByRole('heading', { name: 'Privacy Policy' })).toBeVisible();
    await expect(page.getByText('Our Privacy Commitment')).toBeVisible();
    await expect(page.getByText('What We Don\'t Collect')).toBeVisible();
    await expect(page.getByText('End-to-End Encryption')).toBeVisible();
    await expect(page.getByText('Privacy by Design')).toBeVisible();
  });

  test('should validate file types and sizes', async ({ page }) => {
    await page.goto('/');

    // Test with a potentially dangerous file type
    const dangerousFileName = 'malware.exe';
    const fileContent = 'Not actually malware, just a test';

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: dangerousFileName,
      mimeType: 'application/octet-stream',
      buffer: Buffer.from(fileContent),
    });

    // Should show error for dangerous file type
    await expect(page.getByText('File type not allowed for security reasons')).toBeVisible();
  });

  test('should show subscription upgrade flow', async ({ page }) => {
    await page.goto('/');

    // Click upgrade button
    await page.getByText('Upgrade to Supporter').click();

    // Should show email input
    await expect(page.getByPlaceholder('Enter your email')).toBeVisible();
    
    // Enter email
    await page.getByPlaceholder('Enter your email').fill('<EMAIL>');
    
    // Click supporter upgrade
    await page.getByText('Supporter - $1/month').click();

    // Should redirect to checkout (in demo mode, this will be a demo URL)
    await page.waitForURL(/checkout/);
  });
});
